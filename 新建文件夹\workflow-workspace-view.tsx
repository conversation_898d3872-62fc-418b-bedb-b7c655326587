import { useState, useEffect, useMemo } from "react"
import { Upload, Play, Eye, FileText, Folder, Clock, CheckCircle, XCircle, AlertCircle, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { useConfirmDialog } from "@/components/ui/confirm-dialog"
import type { AppFile, Workflow, WorkflowResult } from '@shared/types'
import { useLanguage } from '../contexts/language-context'
import { translateErrorMessage } from '../utils/error-translation'

interface WorkflowWorkspaceViewProps {
  selectedWorkflowId?: string | null
  onWorkflowSelect?: (workflowId: string | null) => void
}

export function WorkflowWorkspaceView({ selectedWorkflowId: initialWorkflowId, onWorkflowSelect }: WorkflowWorkspaceViewProps) {
  const { t, language } = useLanguage()
  const [files, setFiles] = useState<AppFile[]>([])
  const [workflows, setWorkflows] = useState<Workflow[]>([])
  const [selectedWorkflowId, setSelectedWorkflowId] = useState<string>('')
  const [isRunning, setIsRunning] = useState(false)
  const [workflowResult, setWorkflowResult] = useState<WorkflowResult | null>(null)
  const [isLoadingWorkflows, setIsLoadingWorkflows] = useState(true)
  const [isDragOver, setIsDragOver] = useState(false)
  const [isPreviewMode, setIsPreviewMode] = useState(false) // 区分预览和执行结果
  const [maxItems, setMaxItems] = useState(1000) // 文件处理上限
  const [isProcessingFiles, setIsProcessingFiles] = useState(false) // 文件处理状态

  // 进度显示相关状态
  const [processingProgress, setProcessingProgress] = useState({
    current: 0,
    total: 0,
    currentFile: '',
    canCancel: true
  })

  // 确认对话框
  const { showConfirm, ConfirmDialog } = useConfirmDialog()

  // 加载工作流（优化性能）
  const loadWorkflows = async () => {
    try {
      setIsLoadingWorkflows(true)
      console.log('开始加载工作流 - 工作区视图')

      const startTime = performance.now()
      const allWorkflows = await window.electronAPI.getAllWorkflows()
      const loadTime = performance.now() - startTime
      console.log(`工作流加载耗时: ${loadTime.toFixed(2)}ms`)

      const enabledWorkflows = allWorkflows.filter(workflow => workflow.enabled)
      setWorkflows(enabledWorkflows)
      console.log(`已启用工作流数量: ${enabledWorkflows.length}`)
    } catch (error) {
      console.error('Failed to load workflows:', error)
      setWorkflows([])
    } finally {
      setIsLoadingWorkflows(false)
    }
  }

  // 处理文件拖拽
  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)

    // 检查是否已选择规则
    if (!selectedWorkflowId) {
      return
    }

    // 防止重复处理
    if (isProcessingFiles) {
      return
    }

    const droppedFiles = Array.from(e.dataTransfer.files)
    const filePaths = droppedFiles.map(file => (file as any).path)

    setIsProcessingFiles(true)
    try {
      const processedFiles = await window.electronAPI.processDroppedPaths(filePaths, selectedWorkflowId)

      // 使用统一的文件上限检查
      const limitCheck = await checkFileLimit(files.length, processedFiles.length);

      if (!limitCheck.isValid) {
        showFileLimitWarning(limitCheck);
        return;
      }

      setFiles(prev => [...prev, ...processedFiles])
      setWorkflowResult(null) // 清除之前的结果

      // 验证文件与工作流的匹配性，给出友好提示
      if (selectedWorkflowId && processedFiles.length > 0) {
        await validateFilesWithWorkflow(processedFiles, selectedWorkflowId);
      }
    } catch (error) {
      console.error('Error processing dropped files:', error)
      const errorMessage = error instanceof Error ? error.message : String(error);
      showConfirm({
        title: t('workspace.fileProcessingFailed'),
        description: errorMessage,
        variant: 'destructive',
        confirmText: t('common.confirm'),
        onConfirm: () => {}
      })
    } finally {
      setIsProcessingFiles(false)
    }
  }

  // 统一的文件上限检查函数
  const checkFileLimit = async (currentCount: number, addingCount: number) => {
    try {
      const maxItems = await window.electronAPI.getSetting('workflow.processing.maxItems') || 1000;
      const newTotal = currentCount + addingCount;
      return {
        isValid: newTotal <= maxItems,
        maxItems,
        currentCount,
        addingCount,
        newTotal
      };
    } catch (error) {
      console.error('Failed to get file limit setting:', error);
      // 使用组件状态作为后备
      const newTotal = currentCount + addingCount;
      return {
        isValid: newTotal <= maxItems,
        maxItems,
        currentCount,
        addingCount,
        newTotal
      };
    }
  };

  // 显示文件上限超出警告
  const showFileLimitWarning = (limitCheck: any) => {
    showConfirm({
      title: t('workspace.fileLimitExceeded'),
      description: t('workspace.fileLimitExceededDesc', {
        current: limitCheck.currentCount,
        adding: limitCheck.addingCount,
        total: limitCheck.newTotal,
        limit: limitCheck.maxItems
      }),
      variant: 'warning',
      confirmText: t('common.confirm'),
      onConfirm: () => {}
    });
  };

  // 验证文件与工作流的匹配性
  const validateFilesWithWorkflow = async (uploadedFiles: AppFile[], workflowId: string) => {
    try {
      const workflow = workflows.find(w => w.id === workflowId);
      if (!workflow) return;

      const enabledSteps = workflow.steps.filter(s => s.enabled);
      if (enabledSteps.length === 0) return;

      // 分析文件类型
      const fileCount = uploadedFiles.filter(f => !f.isDirectory).length;
      const folderCount = uploadedFiles.filter(f => f.isDirectory).length;

      // 分析步骤需求
      const needsFiles = enabledSteps.some(step => (step.processTarget || 'files') === 'files');
      const needsFolders = enabledSteps.some(step => step.processTarget === 'folders');

      let hasIssue = false;
      let message = '';
      let suggestion = '';

      if (fileCount > 0 && folderCount === 0 && !needsFiles && needsFolders) {
        // 只有文件，但工作流只需要文件夹
        hasIssue = true;
        message = t('workspace.onlyFilesUploaded', { fileCount });
        suggestion = t('workspace.workflowNeedsFolders');
      } else if (folderCount > 0 && fileCount === 0 && needsFiles && !needsFolders) {
        // 只有文件夹，但工作流只需要文件
        hasIssue = true;
        message = t('workspace.onlyFoldersUploaded', { folderCount });
        suggestion = t('workspace.workflowNeedsFiles');
      }

      if (hasIssue) {
        showConfirm({
          title: t('workspace.fileTypeMismatch'),
          description: `${message}\n\n${suggestion}`,
          variant: 'warning',
          confirmText: t('common.understand'),
          onConfirm: () => {}
        });
      }
    } catch (error) {
      console.error('Error validating files with workflow:', error);
    }
  };

  // 使用useMemo优化预览结果计算 - 改进版本，显示所有步骤的变化
  const previewChangedFiles = useMemo(() => {
    if (!workflowResult || !isPreviewMode) return [];

    // 收集所有步骤中有变化的文件，避免重复
    const allChangedFiles = new Map();

    workflowResult.stepResults.forEach(stepResult => {
      stepResult.outputFiles.forEach(file => {
        if (file.newPath && file.newPath !== file.path) {
          // 使用文件ID作为key，确保每个文件只显示一次（显示最终状态）
          allChangedFiles.set(file.id, file);
        }
      });
    });

    return Array.from(allChangedFiles.values()).slice(0, 20);
  }, [workflowResult, isPreviewMode]);

  const executionDisplayFiles = useMemo(() => {
    if (!workflowResult || isPreviewMode) return [];

    const finalStepResult = workflowResult.stepResults[workflowResult.stepResults.length - 1];
    return finalStepResult ? finalStepResult.outputFiles.slice(0, 20) : [];
  }, [workflowResult, isPreviewMode]);

  // 生成智能建议 - 简化版本，更准确地检测匹配问题
  const generateSmartSuggestions = (inputFiles: any[], workflow: any) => {
    const suggestions: string[] = [];

    if (!workflow) return suggestions;

    const fileCount = inputFiles.filter((f: any) => !f.isDirectory).length;
    const folderCount = inputFiles.filter((f: any) => f.isDirectory).length;
    const enabledSteps = workflow.steps.filter((s: any) => s.enabled);

    if (enabledSteps.length === 0) return suggestions;

    // 检查是否至少有一个步骤可以处理输入文件
    let hasAnyMatchingStep = false;

    for (const step of enabledSteps) {
      const needsFiles = step.processTarget === 'files' || !step.processTarget;
      const needsFolders = step.processTarget === 'folders';

      // 检查这个步骤是否有匹配的文件类型
      if ((needsFiles && fileCount > 0) || (needsFolders && folderCount > 0)) {
        hasAnyMatchingStep = true;
        break;
      }
    }

    // 如果没有任何步骤可以处理输入文件，显示警告
    if (!hasAnyMatchingStep && (fileCount > 0 || folderCount > 0)) {
      if (fileCount > 0 && folderCount === 0) {
        // 只有文件，但所有步骤都要求文件夹
        suggestions.push(t('workspace.stepNeedsFolders', { stepName: '工作流' }));
      } else if (folderCount > 0 && fileCount === 0) {
        // 只有文件夹，但所有步骤都要求文件
        suggestions.push(t('workspace.stepNeedsFiles', { stepName: '工作流' }));
      } else if (fileCount > 0 && folderCount > 0) {
        // 有文件和文件夹，但没有步骤能处理（这种情况比较少见）
        suggestions.push(t('workspace.cannotProcessInputType'));
      }
    }

    return suggestions;
  };

  // 预览工作流执行结果
  const handlePreview = async () => {
    if (!selectedWorkflowId) return

    const selectedWorkflow = workflows.find(w => w.id === selectedWorkflowId)
    if (!selectedWorkflow) return

    let filesToPreview = files

    // 如果没有手动选择文件，但工作流可以一键执行，则自动加载文件
    if (files.length === 0 && canExecuteDirectly(selectedWorkflow)) {
      const inputPath = getWorkflowInputPath(selectedWorkflow)
      if (inputPath) {
        try {
          const defaultFiles = await window.electronAPI.processDroppedPaths([inputPath], selectedWorkflowId)
          filesToPreview = defaultFiles
          setFiles(defaultFiles) // 更新UI显示
        } catch (error) {
          console.error(t('workspace.loadDefaultFilesFailed'), error)
          showConfirm({
            title: t('workspace.loadFailed'),
            description: t('workspace.loadFailedDesc', { path: inputPath }),
            variant: 'destructive',
            confirmText: t('common.confirm'),
            onConfirm: () => {}
          })
          return
        }
      }
    }

    // 如果仍然没有文件可预览，则返回
    if (filesToPreview.length === 0) return

    // 生成智能建议
    const suggestions = generateSmartSuggestions(filesToPreview, selectedWorkflow);

    // 如果有明显的不匹配问题，先显示警告
    if (suggestions.length > 0) {
      const shouldContinue = await new Promise<boolean>((resolve) => {
        showConfirm({
          title: t('workspace.inputMismatchWarning'),
          description: `${t('workspace.detectedIssues')}:\n${suggestions.join('\n')}\n\n${t('workspace.continuePreview')}`,
          variant: 'warning',
          confirmText: t('workspace.continuePreview'),
          cancelText: t('common.cancel'),
          onConfirm: () => resolve(true),
          onCancel: () => resolve(false)
        });
      });

      if (!shouldContinue) return;
    }

    try {
      const result = await window.electronAPI.previewWorkflow(filesToPreview, selectedWorkflow)
      setWorkflowResult(result)
      setIsPreviewMode(true) // 设置为预览模式
    } catch (error) {
      console.error('Preview failed:', error)
      showConfirm({
        title: t('common.error'),
        description: t('workspace.previewFailed'),
        variant: 'warning',
        confirmText: t('common.confirm'),
        onConfirm: () => {}
      })
    }
  }

  // 检查工作流是否可以一键执行
  const canExecuteDirectly = (workflow: Workflow): boolean => {
    // 如果工作流有默认输入路径，可以一键执行
    if (workflow.defaultInputPath) return true

    // 如果第一个启用的步骤有指定路径输入源，也可以一键执行
    const firstEnabledStep = workflow.steps
      .filter(step => step.enabled)
      .sort((a, b) => a.order - b.order)[0]

    return firstEnabledStep?.inputSource.type === 'specific_path' && !!firstEnabledStep.inputSource.path
  }

  // 获取工作流的输入路径
  const getWorkflowInputPath = (workflow: Workflow): string | null => {
    // 优先使用工作流的默认输入路径
    if (workflow.defaultInputPath) return workflow.defaultInputPath

    // 否则使用第一个启用步骤的指定路径
    const firstEnabledStep = workflow.steps
      .filter(step => step.enabled)
      .sort((a, b) => a.order - b.order)[0]

    if (firstEnabledStep?.inputSource.type === 'specific_path' && firstEnabledStep.inputSource.path) {
      return firstEnabledStep.inputSource.path
    }

    return null
  }

  // 检查工作流是否应该禁用文件选择（已配置了指定路径）
  const shouldDisableFileSelection = (workflow: Workflow): boolean => {
    return canExecuteDirectly(workflow)
  }

  // 监听工作流进度更新
  useEffect(() => {
    const handleWorkflowProgress = (progress: { processed: number; total: number; currentBatch?: number; totalBatches?: number }) => {
      setProcessingProgress(prev => ({
        ...prev,
        current: progress.processed,
        total: progress.total,
        currentFile: progress.currentBatch
          ? t('workspace.processingBatch', { current: progress.currentBatch, total: progress.totalBatches || 1 })
          : t('workspace.processingFiles')
      }))
    }

    // 添加事件监听器
    if (window.electronAPI.onWorkflowProgress) {
      window.electronAPI.onWorkflowProgress(handleWorkflowProgress)
    }

    return () => {
      // 清理事件监听器（如果有提供清理方法的话）
    }
  }, [t])

  // 执行工作流
  const handleExecute = async () => {
    if (!selectedWorkflowId) return

    const selectedWorkflow = workflows.find(w => w.id === selectedWorkflowId)
    if (!selectedWorkflow) return

    let filesToProcess = files

    // 如果没有手动选择文件，但工作流可以一键执行，则自动加载文件
    if (files.length === 0 && canExecuteDirectly(selectedWorkflow)) {
      const inputPath = getWorkflowInputPath(selectedWorkflow)
      if (inputPath) {
        try {
          const defaultFiles = await window.electronAPI.processDroppedPaths([inputPath], selectedWorkflowId)
          filesToProcess = defaultFiles
          setFiles(defaultFiles) // 更新UI显示
        } catch (error) {
          console.error(t('workspace.loadDefaultFilesFailed'), error)
          showConfirm({
            title: t('workspace.loadFailed'),
            description: t('workspace.loadFailedDesc', { path: inputPath }),
            variant: 'destructive',
            confirmText: t('common.confirm'),
            onConfirm: () => {}
          })
          return
        }
      }
    }

    // 如果仍然没有文件可处理，则提示用户
    if (filesToProcess.length === 0) {
      showConfirm({
        title: t('workspace.noFilesTitle'),
        description: t('workspace.noFilesDesc'),
        variant: 'default',
        confirmText: t('common.confirm'),
        onConfirm: () => {}
      })
      return
    }

    setIsRunning(true)
    setProcessingProgress({
      current: 0,
      total: filesToProcess.length,
      currentFile: t('workspace.preparingProcess'),
      canCancel: true
    })

    try {
      // 执行工作流，进度更新通过事件监听器处理
      const result = await window.electronAPI.executeWorkflow(filesToProcess, selectedWorkflow)

      setWorkflowResult(result)
      setIsPreviewMode(false) // 设置为执行模式

      // 更新文件状态 - 使用最终步骤的输出文件
      const finalStepResult = result.stepResults[result.stepResults.length - 1];
      if (finalStepResult) {
        // 创建文件ID到最终状态的映射
        const finalFileMap = new Map(finalStepResult.outputFiles.map(file => [file.id, file]));

        const updatedFiles = filesToProcess.map(file => {
          const finalFile = finalFileMap.get(file.id);
          return finalFile || file; // 如果找不到最终状态，保持原状态
        });

        setFiles(updatedFiles);
      }

      // 生成详细的执行结果描述
      const stepSummary = result.stepResults.map((step, index) => {
        const processedInStep = step.outputFiles.filter(file =>
          file.status === 'success' && file.newPath
        ).length;
        return t('workspace.stepSummary', {
          stepNumber: index + 1,
          stepName: step.stepName,
          itemCount: processedInStep
        });
      }).join('\n');

      const processedItems = result.processedFiles;
      const errorCount = result.errors.length;

      let description = t('workspace.totalProcessed', { count: processedItems });
      if (errorCount > 0) {
        description += t('workspace.withErrors', { errorCount });
      }
      description += `\n\n${t('workspace.detailsLabel')}\n${stepSummary}`;

      showConfirm({
        title: t('workspace.executionCompleteTitle'),
        description: description,
        variant: 'default',
        confirmText: t('common.confirm'),
        onConfirm: () => {}
      })
    } catch (error) {
      console.error('Execution failed:', error)
      showConfirm({
        title: t('workspace.executionFailed'),
        description: t('workspace.executionFailedDesc'),
        variant: 'destructive',
        confirmText: t('common.confirm'),
        onConfirm: () => {}
      })
    } finally {
      setIsRunning(false)
      setProcessingProgress({
        current: 0,
        total: 0,
        currentFile: '',
        canCancel: true
      })
    }
  }

  // 取消工作流执行
  const handleCancelExecution = () => {
    // 这里应该调用后端的取消接口
    setIsRunning(false)
    setProcessingProgress({
      current: 0,
      total: 0,
      currentFile: '',
      canCancel: true
    })
  }

  // 清空文件列表
  const handleClearFiles = () => {
    setFiles([])
    setWorkflowResult(null)
  }



  // 选择文件
  const handleSelectFiles = async () => {
    // 检查是否已选择规则
    if (!selectedWorkflowId) {
      return
    }

    // 防止重复处理
    if (isProcessingFiles) {
      return
    }

    setIsProcessingFiles(true)
    try {
      const filePaths = await window.electronAPI.openFile()
      if (filePaths.length > 0) {
        // 使用统一的文件上限检查
        const limitCheck = await checkFileLimit(files.length, filePaths.length);

        if (!limitCheck.isValid) {
          showFileLimitWarning(limitCheck);
          return;
        }

        const processedFiles = await window.electronAPI.processDroppedPaths(filePaths, selectedWorkflowId)
        setFiles(prev => [...prev, ...processedFiles])
        setWorkflowResult(null)

        // 验证文件与工作流的匹配性
        if (selectedWorkflowId && processedFiles.length > 0) {
          await validateFilesWithWorkflow(processedFiles, selectedWorkflowId);
        }
      }
    } catch (error) {
      console.error(t('error.selectFilesFailed'), error)
      const errorMessage = error instanceof Error ? error.message : String(error);
      showConfirm({
        title: t('workspace.fileProcessingFailed'),
        description: errorMessage,
        variant: 'destructive',
        confirmText: t('common.confirm'),
        onConfirm: () => {}
      })
    } finally {
      setIsProcessingFiles(false)
    }
  }

  // 选择文件夹
  const handleSelectFolder = async () => {
    // 检查是否已选择规则
    if (!selectedWorkflowId) {
      return
    }

    // 防止重复处理
    if (isProcessingFiles) {
      return
    }

    setIsProcessingFiles(true)
    try {
      const folderPath = await window.electronAPI.openDirectory()
      if (folderPath) {
        const processedFiles = await window.electronAPI.processDroppedPaths([folderPath], selectedWorkflowId)

        // 使用统一的文件上限检查
        const limitCheck = await checkFileLimit(files.length, processedFiles.length);

        if (!limitCheck.isValid) {
          showFileLimitWarning(limitCheck);
          return;
        }

        setFiles(prev => [...prev, ...processedFiles])
        setWorkflowResult(null)

        // 验证文件与工作流的匹配性
        if (selectedWorkflowId && processedFiles.length > 0) {
          await validateFilesWithWorkflow(processedFiles, selectedWorkflowId);
        }
      }
    } catch (error) {
      console.error(t('error.selectFolderFailed'), error)
      const errorMessage = error instanceof Error ? error.message : String(error);
      showConfirm({
        title: t('workspace.fileProcessingFailed'),
        description: errorMessage,
        variant: 'destructive',
        confirmText: t('common.confirm'),
        onConfirm: () => {}
      })
    } finally {
      setIsProcessingFiles(false)
    }
  }

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-400" />
      case 'error':
        return <XCircle className="w-4 h-4 text-red-400" />
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-400" />
      default:
        return <AlertCircle className="w-4 h-4 text-text-tertiary" />
    }
  }

  useEffect(() => {
    loadWorkflows()

    // 加载文件处理上限设置
    const loadSettings = async () => {
      try {
        const limit = await window.electronAPI.getSetting('workflow.processing.maxItems') || 1000
        setMaxItems(limit)
      } catch (error) {
        console.error('Failed to load max items setting:', error)
      }
    }

    loadSettings()
  }, [])

  // 监听语言变化，重新加载工作流以更新默认工作流的名称和描述
  useEffect(() => {
    const updateWorkflowsLanguage = async () => {
      try {
        // 添加小延迟，确保语言上下文中的默认工作流语言更新完成
        await new Promise(resolve => setTimeout(resolve, 100))
        await loadWorkflows()
      } catch (error) {
        console.error('重新加载工作流失败:', error)
      }
    }

    updateWorkflowsLanguage()
  }, [language])

  // 调试：打印当前工作流数据
  useEffect(() => {
    if (workflows.length > 0) {
      console.log('当前工作流数据:', workflows.map(w => ({
        id: w.id,
        name: w.name,
        defaultInputPath: w.defaultInputPath
      })))
    }
  }, [workflows])

  // 当从工作流中心传入工作流ID时，自动选择该工作流
  useEffect(() => {
    if (initialWorkflowId && workflows.length > 0) {
      const workflow = workflows.find(w => w.id === initialWorkflowId)
      if (workflow) {
        setSelectedWorkflowId(initialWorkflowId)
        onWorkflowSelect?.(initialWorkflowId)
      }
    }
  }, [initialWorkflowId, workflows, onWorkflowSelect])

  return (
    <div className="h-full p-6">
      <div className="grid grid-cols-1 xl:grid-cols-4 lg:grid-cols-3 gap-6 h-full">
        
        {/* 左侧：文件上传区域 */}
        <div className="lg:col-span-1">
          <Card className="h-full bg-bg-secondary border-border-primary shadow-lg">
            <CardHeader className="pb-4">
              <CardTitle className="text-text-primary flex items-center text-lg font-semibold">
                <Upload className="w-5 h-5 mr-3 text-blue-400" />
                {t('workspace.selectFilesTab')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">

              {/* 拖拽区域 */}
              <div
                className={`relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 ${
                  (() => {
                    if (!selectedWorkflowId) {
                      return 'border-border-secondary bg-bg-secondary/30 opacity-60 cursor-not-allowed'
                    }

                    const selectedWorkflow = workflows.find(w => w.id === selectedWorkflowId)
                    const isDragDisabled = selectedWorkflow && shouldDisableFileSelection(selectedWorkflow)

                    if (isDragDisabled) {
                      return 'border-border-secondary bg-bg-secondary/30 opacity-60 cursor-not-allowed'
                    }

                    return isDragOver
                      ? 'border-blue-400 bg-gradient-to-br from-blue-900/30 to-blue-800/20 shadow-lg shadow-blue-500/20'
                      : 'border-border-secondary hover:border-border-primary hover:bg-bg-tertiary/50'
                  })()
                }`}
                onDrop={(() => {
                  if (!selectedWorkflowId) return undefined
                  const selectedWorkflow = workflows.find(w => w.id === selectedWorkflowId)
                  const isDragDisabled = selectedWorkflow && shouldDisableFileSelection(selectedWorkflow)
                  return isDragDisabled ? undefined : handleDrop
                })()}
                onDragOver={(() => {
                  if (!selectedWorkflowId) return undefined
                  const selectedWorkflow = workflows.find(w => w.id === selectedWorkflowId)
                  const isDragDisabled = selectedWorkflow && shouldDisableFileSelection(selectedWorkflow)
                  return isDragDisabled ? undefined : (e) => {
                    e.preventDefault()
                    setIsDragOver(true)
                  }
                })()}
                onDragLeave={(() => {
                  if (!selectedWorkflowId) return undefined
                  const selectedWorkflow = workflows.find(w => w.id === selectedWorkflowId)
                  const isDragDisabled = selectedWorkflow && shouldDisableFileSelection(selectedWorkflow)
                  return isDragDisabled ? undefined : () => setIsDragOver(false)
                })()}
              >
                {/* 背景装饰 */}
                <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-transparent via-gray-700/10 to-gray-600/20 pointer-events-none" />

                <div className="relative z-10">
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center transition-all duration-300 ${
                    !selectedWorkflowId
                      ? 'bg-bg-tertiary/30 text-text-tertiary'
                      : isDragOver
                      ? 'bg-blue-500/20 text-blue-400'
                      : 'bg-bg-tertiary/50 text-text-tertiary hover:bg-bg-quaternary/50 hover:text-text-secondary'
                  }`}>
                    <Folder className="w-8 h-8" />
                  </div>

                  <h3 className={`text-lg font-medium mb-2 transition-colors ${
                    !selectedWorkflowId
                      ? 'text-text-tertiary'
                      : isDragOver ? 'text-blue-300' : 'text-text-secondary'
                  }`}>
                    {!selectedWorkflowId ? t('workspace.pleaseSelectRule') : t('workspace.dragDropArea')}
                  </h3>



                  {/* 选择按钮 */}
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    {(() => {
                      const selectedWorkflow = workflows.find(w => w.id === selectedWorkflowId)
                      const isFileSelectionDisabled = !selectedWorkflowId || (selectedWorkflow && shouldDisableFileSelection(selectedWorkflow))

                      return (
                        <>
                          <Button
                            size="default"
                            variant="outline"
                            onClick={handleSelectFiles}
                            disabled={isFileSelectionDisabled}
                            className={`transition-all duration-200 shadow-md ${
                              isFileSelectionDisabled
                                ? 'bg-bg-tertiary/30 border-border-secondary text-text-tertiary cursor-not-allowed opacity-50'
                                : 'bg-bg-tertiary/80 border-border-secondary hover:bg-bg-quaternary hover:border-border-primary text-text-secondary hover:text-text-primary hover:shadow-lg'
                            }`}
                          >
                            <FileText className="w-4 h-4 mr-2" />
                            {t('workspace.selectFilesButton')}
                          </Button>
                          <Button
                            size="default"
                            variant="outline"
                            onClick={handleSelectFolder}
                            disabled={isFileSelectionDisabled}
                            className={`transition-all duration-200 shadow-md ${
                              isFileSelectionDisabled
                                ? 'bg-bg-tertiary/30 border-border-secondary text-text-tertiary cursor-not-allowed opacity-50'
                                : 'bg-bg-tertiary/80 border-border-secondary hover:bg-bg-quaternary hover:border-border-primary text-text-secondary hover:text-text-primary hover:shadow-lg'
                            }`}
                          >
                            <Folder className="w-4 h-4 mr-2" />
                            {t('workspace.selectFolderButton')}
                          </Button>
                        </>
                      )
                    })()}
                  </div>
                </div>
              </div>



              {/* 工作流选择 */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  <label className="text-base font-medium text-text-secondary">
                    {t('workspace.selectRule')}
                  </label>
                </div>
                {isLoadingWorkflows ? (
                  <div className="text-center text-text-tertiary py-6 bg-bg-tertiary/50 rounded-lg border border-border-secondary">
                    <div className="animate-spin w-5 h-5 border-2 border-text-tertiary border-t-transparent rounded-full mx-auto mb-2"></div>
                    {t('workspace.loadingRules')}
                  </div>
                ) : (
                  <Select value={selectedWorkflowId} onValueChange={(value) => {
                    // 完整的工作流切换处理
                    setSelectedWorkflowId(value)
                    setWorkflowResult(null) // 清除之前的结果
                    setIsPreviewMode(false) // 重置预览模式

                    // 清理文件状态，重置为待处理状态
                    setFiles(prev => prev.map(file => ({
                      ...file,
                      status: 'pending',
                      error: undefined,
                      newPath: undefined
                    })))

                    // 重置进度状态
                    setProcessingProgress({
                      current: 0,
                      total: 0,
                      currentFile: '',
                      canCancel: true
                    })

                    onWorkflowSelect?.(value)
                  }}>
                    <SelectTrigger className="bg-bg-tertiary/80 border-border-secondary text-text-secondary hover:bg-bg-quaternary/80 hover:border-border-primary transition-all duration-200 h-12 shadow-sm">
                      <SelectValue placeholder={t('workspace.selectRulePlaceholder')} />
                    </SelectTrigger>
                    <SelectContent className="bg-bg-tertiary border-border-secondary shadow-xl">
                      {workflows.map((workflow) => (
                        <SelectItem
                          key={workflow.id}
                          value={workflow.id}
                          className="text-text-secondary focus:bg-bg-quaternary focus:text-text-primary hover:bg-bg-quaternary transition-colors"
                        >
                          <div className="flex items-center space-x-2">
                            <div className={`w-2 h-2 rounded-full ${workflow.enabled ? 'bg-green-400' : 'bg-text-tertiary'}`}></div>
                            <span>{workflow.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}

                {/* 一键执行模式提示 */}
                {selectedWorkflowId && (() => {
                  const selectedWorkflow = workflows.find(w => w.id === selectedWorkflowId)
                  if (!selectedWorkflow || !canExecuteDirectly(selectedWorkflow)) return null

                  const inputPath = getWorkflowInputPath(selectedWorkflow)
                  return (
                    <div className="bg-blue-100 dark:bg-blue-900/20 border border-blue-300 dark:border-blue-500/30 rounded-lg p-3">
                      <div className="flex items-start space-x-2">
                        <div className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm text-blue-800 dark:text-blue-300 font-medium">{t('workspace.oneClickMode')}</p>
                          <p className="text-xs text-blue-700 dark:text-blue-200/80 mt-1">
                            {t('workspace.oneClickModeDesc', { path: inputPath })}
                          </p>
                          <p className="text-xs text-blue-600 dark:text-blue-200/60 mt-1">
                            {t('workspace.oneClickModeHint')}
                          </p>
                        </div>
                      </div>
                    </div>
                  )
                })()}
              </div>

              {/* 操作按钮 */}
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-3">
                  <Button
                    onClick={handlePreview}
                    disabled={!selectedWorkflowId || (files.length === 0 && !canExecuteDirectly(workflows.find(w => w.id === selectedWorkflowId) || {} as Workflow))}
                    className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 h-12 font-medium disabled:from-gray-600 disabled:to-gray-700 disabled:text-gray-400"
                  >
                    <Eye className="w-5 h-5 mr-2" />
                    {t('workspace.preview')}
                  </Button>

                  <Button
                    onClick={handleExecute}
                    disabled={!selectedWorkflowId || (files.length === 0 && !canExecuteDirectly(workflows.find(w => w.id === selectedWorkflowId) || {} as Workflow)) || isRunning}
                    className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-lg hover:shadow-xl transition-all duration-200 h-12 font-medium disabled:from-gray-600 disabled:to-gray-700 disabled:text-gray-400"
                  >
                    <Play className="w-5 h-5 mr-2" />
                    {isRunning ? t('workspace.processing') : t('workspace.execute')}
                  </Button>
                </div>

                <div className="pt-2 border-t border-gray-600/30 dark:border-gray-400/35">
                  <Button
                    onClick={handleClearFiles}
                    variant="outline"
                    className="w-full bg-bg-tertiary/50 border-border-secondary hover:bg-bg-quaternary/70 hover:border-border-primary text-text-secondary hover:text-text-primary transition-all duration-200 h-10"
                  >
                    {t('workspace.clearFiles')}
                  </Button>
                </div>
              </div>

              {/* 文件统计 */}
              {files.length > 0 && (
                <div className="bg-gradient-to-r from-bg-tertiary to-bg-quaternary dark:from-gray-750 dark:to-gray-700 rounded-xl p-4 border border-border-secondary/50 shadow-inner">
                  <div className="flex items-center justify-between mb-3">
                    {(() => {
                      let fileCount, folderCount, totalCount, isProcessed = false;

                      // 如果有工作流结果，显示实际会被处理的文件统计
                      if (workflowResult && workflowResult.stepResults.length > 0) {
                        // 获取所有步骤的输入文件（去重）
                        const allProcessedFiles = new Map();
                        workflowResult.stepResults.forEach(stepResult => {
                          stepResult.inputFiles.forEach(file => {
                            allProcessedFiles.set(file.id, file);
                          });
                        });
                        const processedFilesArray = Array.from(allProcessedFiles.values());
                        fileCount = processedFilesArray.filter(f => !f.isDirectory).length;
                        folderCount = processedFilesArray.filter(f => f.isDirectory).length;
                        totalCount = allProcessedFiles.size;
                        isProcessed = true;
                      } else {
                        // 没有工作流结果时，根据选中的工作流步骤配置来过滤显示
                        console.log('顶部统计 - 开始过滤逻辑');
                        console.log('顶部统计 - selectedWorkflowId:', selectedWorkflowId);
                        console.log('顶部统计 - workflows数量:', workflows.length);

                        const selectedWorkflow = workflows.find(w => w.id === selectedWorkflowId);
                        console.log('顶部统计 - 找到的工作流:', selectedWorkflow?.name);

                        if (selectedWorkflow && selectedWorkflow.steps.length > 0) {
                          // 获取所有启用步骤的处理目标
                          const enabledSteps = selectedWorkflow.steps.filter(s => s.enabled);
                          const processTargets = enabledSteps.map(s => s.processTarget || 'files');

                          console.log('顶部统计过滤 - 工作流:', selectedWorkflow.name);
                          console.log('顶部统计过滤 - 处理目标:', processTargets);

                          // 根据步骤配置过滤文件
                          let filteredFiles = files;
                          if (processTargets.includes('files') && processTargets.includes('folders')) {
                            // 包含文件和文件夹处理步骤，显示所有
                            filteredFiles = files;
                            console.log('顶部统计过滤 - 显示所有文件和文件夹');
                          } else if (processTargets.includes('folders')) {
                            // 只有文件夹处理步骤，只显示文件夹
                            filteredFiles = files.filter(f => f.isDirectory);
                            console.log('顶部统计过滤 - 只显示文件夹，过滤后数量:', filteredFiles.length);
                          } else {
                            // 只有文件处理步骤，只显示文件
                            filteredFiles = files.filter(f => !f.isDirectory);
                            console.log('顶部统计过滤 - 只显示文件，过滤后数量:', filteredFiles.length);
                          }

                          fileCount = filteredFiles.filter(f => !f.isDirectory).length;
                          folderCount = filteredFiles.filter(f => f.isDirectory).length;
                          totalCount = filteredFiles.length;
                        } else {
                          // 没有选中工作流或工作流没有步骤，显示所有文件
                          fileCount = files.filter(f => !f.isDirectory).length;
                          folderCount = files.filter(f => f.isDirectory).length;
                          totalCount = files.length;
                        }
                        isProcessed = false;
                      }

                      return (
                        <>
                          <div className="flex items-center space-x-3 flex-1 min-w-0">
                            <div className={`w-2 h-2 rounded-full animate-pulse flex-shrink-0 ${
                              totalCount >= maxItems ? 'bg-red-400' :
                              totalCount >= maxItems * 0.8 ? 'bg-yellow-400' :
                              'bg-green-400'
                            }`}></div>
                            <div className="flex-1 min-w-0">
                              <div className="flex flex-col">
                                <span className="text-text-secondary font-medium text-sm">
                                  {isProcessed ? t('workspace.selectedCountPending', { count: totalCount }) : t('workspace.selectedCount', { count: totalCount })}
                                </span>
                                <span className="text-text-tertiary text-xs">
                                  {fileCount > 0 && t('workspace.fileCountUnit', { count: fileCount })}
                                  {fileCount > 0 && folderCount > 0 && ' • '}
                                  {folderCount > 0 && t('workspace.folderCountUnit', { count: folderCount })}
                                  {totalCount > 0 && ` • ${t('workspace.limitInfo', { limit: maxItems })}`}
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2 flex-shrink-0">
                            <span className={`font-bold text-xl ${
                              totalCount >= maxItems ? 'text-red-400' :
                              totalCount >= maxItems * 0.8 ? 'text-yellow-400' :
                              'text-blue-400'
                            }`}>
                              {totalCount}
                            </span>
                            <span className="text-text-tertiary text-sm">
                              {t('workspace.itemsUnit')}
                            </span>
                          </div>
                        </>
                      );
                    })()}
                  </div>

                  {/* 多路径信息卡片 */}
                  <div className="mb-3">
                    <div className="space-y-2 max-h-32 overflow-y-auto scrollbar-hide">
                      {(() => {
                        // 按根目录分组文件
                        const pathGroups = files.reduce((acc, file) => {
                          const rootPath = file.originalDir || (file.isDirectory ? file.path : require('path').dirname(file.path));
                          if (!acc[rootPath]) {
                            acc[rootPath] = {
                              files: [],
                              fileCount: 0,
                              folderCount: 0,
                              typeStats: {} as Record<string, number>
                            };
                          }

                          acc[rootPath].files.push(file);

                          if (file.isDirectory) {
                            acc[rootPath].folderCount++;
                            acc[rootPath].typeStats[t('workspace.folder')] = (acc[rootPath].typeStats[t('workspace.folder')] || 0) + 1;
                          } else {
                            acc[rootPath].fileCount++;
                            const ext = file.name.split('.').pop()?.toLowerCase() || t('workspace.noExtension');
                            acc[rootPath].typeStats[ext] = (acc[rootPath].typeStats[ext] || 0) + 1;
                          }

                          return acc;
                        }, {} as Record<string, { files: typeof files, fileCount: number, folderCount: number, typeStats: Record<string, number> }>);

                        return Object.entries(pathGroups).map(([rootPath, group]) => {
                          // 截断路径显示
                          const displayPath = rootPath.length > 40
                            ? '...' + rootPath.slice(-37)
                            : rootPath;

                          const totalCount = group.fileCount + group.folderCount;

                          return (
                            <div key={rootPath} className="bg-bg-quaternary/30 rounded-lg p-3 border border-border-secondary/20">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center space-x-2 flex-1 min-w-0">
                                  <Folder className="w-4 h-4 text-blue-500 flex-shrink-0" />
                                  <span className="text-xs text-text-secondary truncate" title={rootPath}>
                                    {displayPath}
                                  </span>
                                </div>
                                <span className="text-xs text-blue-400 font-medium flex-shrink-0">
                                  {totalCount} {t('workspace.itemsUnit')}
                                </span>
                              </div>

                              <div className="flex flex-wrap gap-1">
                                {Object.entries(group.typeStats).slice(0, 3).map(([type, count]) => (
                                  <div key={type} className="bg-bg-quaternary/50 px-2 py-1 rounded-md text-xs">
                                    <span className="text-text-secondary">{type}</span>
                                    <span className="text-blue-400 ml-1">×{count}</span>
                                  </div>
                                ))}
                                {Object.keys(group.typeStats).length > 3 && (
                                  <div className="bg-bg-quaternary/50 px-2 py-1 rounded-md text-xs">
                                    <span className="text-text-tertiary">+{Object.keys(group.typeStats).length - 3}</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          );
                        });
                      })()}
                    </div>
                  </div>

                  {/* 执行结果统计 */}
                  {workflowResult && (
                    <div className="pt-3 border-t border-border-secondary/20 dark:border-border-secondary/10">
                      <div className="flex flex-wrap gap-2 text-xs">
                        <div className="flex items-center space-x-1 bg-green-500/10 px-2 py-1 rounded-md">
                          <CheckCircle className="w-3 h-3 text-green-400" />
                          <span className="text-text-tertiary">{t('workspace.successLabel')}</span>
                          <span className="text-green-400 font-medium">{workflowResult.processedFiles}</span>
                        </div>
                        <div className="flex items-center space-x-1 bg-blue-500/10 px-2 py-1 rounded-md">
                          <Clock className="w-3 h-3 text-blue-400" />
                          <span className="text-text-tertiary">{t('workspace.durationLabel')}</span>
                          <span className="text-blue-400 font-medium">{workflowResult.duration}ms</span>
                        </div>
                        {workflowResult.errors.length > 0 && (
                          <div className="flex items-center space-x-1 bg-red-500/10 px-2 py-1 rounded-md">
                            <XCircle className="w-3 h-3 text-red-400" />
                            <span className="text-text-tertiary">{t('workspace.errorLabel')}</span>
                            <span className="text-red-400 font-medium">{t('workspace.errorCount', { count: workflowResult.errors.length })}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}


                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 中间：文件列表 */}
        <div className="xl:col-span-2 lg:col-span-2 flex flex-col min-h-0">
          <Card className="h-full bg-bg-secondary border-border-primary flex flex-col min-h-0 shadow-lg backdrop-blur-sm">
            <CardHeader className="flex-shrink-0 pb-3">
              <CardTitle className="text-text-secondary flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                {t('workspace.fileListTab')}
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1 min-h-0 overflow-hidden p-4 pt-0">
              {isRunning ? (
                /* 进度显示区域 */
                <div className="flex items-center justify-center h-full">
                  <div className="w-full max-w-md">
                    <div className="text-center mb-6">
                      <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Clock className="w-8 h-8 text-blue-400 animate-spin" />
                      </div>
                      <h3 className="text-lg font-semibold text-text-secondary mb-2">{t('workspace.processingFiles')}</h3>
                      <p className="text-sm text-text-tertiary">{processingProgress.currentFile}</p>
                    </div>

                    <div className="space-y-4">
                      <Progress
                        value={(processingProgress.current / processingProgress.total) * 100}
                        className="w-full h-2"
                      />
                      <div className="flex justify-between text-sm text-text-tertiary">
                        <span>{processingProgress.current} / {processingProgress.total}</span>
                        <span>{Math.round((processingProgress.current / processingProgress.total) * 100)}%</span>
                      </div>
                    </div>

                    {processingProgress.canCancel && (
                      <div className="mt-6 text-center">
                        <Button
                          onClick={handleCancelExecution}
                          variant="outline"
                          size="sm"
                          className="text-red-400 border-red-400 hover:bg-red-400/10"
                        >
                          <X className="w-4 h-4 mr-2" />
                          {t('workspace.cancelProcessing')}
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ) : files.length === 0 ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <FileText className="w-16 h-16 text-text-tertiary mx-auto mb-4" />
                    <p className="text-text-tertiary">{t('workspace.noFiles')}</p>
                    <p className="text-sm text-text-tertiary mt-2">{t('workspace.dragToStart')}</p>
                  </div>
                </div>
              ) : (
                <div className="h-full min-h-0 overflow-y-auto">
                  <div className="space-y-2">
                  {(() => {
                    // 根据工作流步骤配置过滤要显示的文件
                    let filesToShow = files;

                    if (workflowResult && workflowResult.stepResults.length > 0) {
                      // 如果有工作流结果，显示所有会被处理的文件（从工作流结果中获取）
                      const allProcessedFiles = new Map();
                      workflowResult.stepResults.forEach(stepResult => {
                        stepResult.inputFiles.forEach(file => {
                          allProcessedFiles.set(file.id, file);
                        });
                      });
                      filesToShow = Array.from(allProcessedFiles.values());
                      console.log('文件列表 - 有工作流结果，显示处理的文件数量:', filesToShow.length);
                    } else {
                      // 没有工作流结果时，根据选中的工作流步骤配置来过滤显示
                      console.log('文件列表 - 开始过滤逻辑');
                      console.log('文件列表 - selectedWorkflowId:', selectedWorkflowId);
                      console.log('文件列表 - workflows数量:', workflows.length);

                      const selectedWorkflow = workflows.find(w => w.id === selectedWorkflowId);
                      console.log('文件列表 - 找到的工作流:', selectedWorkflow?.name);

                      if (selectedWorkflow && selectedWorkflow.steps.length > 0) {
                        // 获取所有启用步骤的处理目标
                        const enabledSteps = selectedWorkflow.steps.filter(s => s.enabled);
                        const processTargets = enabledSteps.map(s => s.processTarget || 'files');

                        console.log('文件列表过滤 - 工作流:', selectedWorkflow.name);
                        console.log('文件列表过滤 - 启用步骤数:', enabledSteps.length);
                        console.log('文件列表过滤 - 处理目标:', processTargets);
                        console.log('文件列表过滤 - 原始文件数:', files.length);

                        // 根据步骤配置过滤文件
                        if (processTargets.includes('files') && processTargets.includes('folders')) {
                          // 包含文件和文件夹处理步骤，显示所有
                          filesToShow = files;
                          console.log('文件列表过滤 - 显示所有文件和文件夹');
                        } else if (processTargets.includes('folders')) {
                          // 只有文件夹处理步骤，只显示文件夹
                          filesToShow = files.filter(f => f.isDirectory);
                          console.log('文件列表过滤 - 只显示文件夹，过滤后数量:', filesToShow.length);
                        } else {
                          // 只有文件处理步骤，只显示文件
                          filesToShow = files.filter(f => !f.isDirectory);
                          console.log('文件列表过滤 - 只显示文件，过滤后数量:', filesToShow.length);
                        }
                      }
                    }

                    return filesToShow
                      .sort((a, b) => {
                        // 按文件路径进行自然排序，确保显示顺序正确
                        return a.path.localeCompare(b.path, undefined, {
                          numeric: true,
                          sensitivity: 'base'
                        });
                      })
                      .map((file) => {
                        // 检查这个文件是否会被工作流处理
                        let willBeProcessed = false;
                        if (workflowResult && workflowResult.stepResults.length > 0) {
                          const allProcessedFiles = new Map();
                          workflowResult.stepResults.forEach(stepResult => {
                            stepResult.inputFiles.forEach(inputFile => {
                              allProcessedFiles.set(inputFile.id, inputFile);
                            });
                          });
                          willBeProcessed = allProcessedFiles.has(file.id);
                        }

                        return (
                    <div
                      key={file.id}
                      className={`flex items-center justify-between p-3 rounded-lg shadow-sm ${
                        willBeProcessed
                          ? "bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/20 border border-blue-200 dark:border-blue-500/30"
                          : workflowResult
                            ? "bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800/30 dark:to-gray-700/20 border border-gray-200 dark:border-gray-600/30 opacity-60"
                            : "bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/20 border border-blue-200 dark:border-blue-500/30"
                      }`}
                    >
                      <div className="flex items-center space-x-3 flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          {file.isDirectory ? (
                            <Folder className="w-4 h-4 text-blue-500" />
                          ) : (
                            <FileText className="w-4 h-4 text-text-tertiary" />
                          )}
                          {getStatusIcon(file.status)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-text-secondary truncate">
                            {file.name}
                          </p>
                          <p className="text-xs text-text-tertiary truncate">
                            {file.path}
                          </p>
                          {file.isDirectory && (
                            <p className="text-xs text-text-tertiary">
                              {file.fileCount !== undefined && file.folderCount !== undefined ? (
                                `${file.fileCount} ${t('workspace.files')}, ${file.folderCount} ${t('workspace.folders')}`
                              ) : file.isEmpty ? (
                                t('workspace.emptyFolder')
                              ) : (
                                t('workspace.folder')
                              )}
                            </p>
                          )}
                          {file.newPath && file.newPath !== file.path && (
                            <p className="text-xs text-blue-400 truncate">
                              → {file.newPath}
                            </p>
                          )}
                          {file.error && (
                            <p className="text-xs text-red-400">
                              {t('workspace.errorPrefix')}{translateErrorMessage(file.error, language)}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="text-xs text-text-tertiary">
                        {file.isDirectory ? t('workspace.folder').toUpperCase() : file.type.toUpperCase()}
                      </div>
                    </div>
                        );
                      });
                  })()}
                  </div>
                </div>
              )}

              {/* 移动端预览结果 - 在卡片内部显示，设置固定高度 */}
              {workflowResult && (
                <div className="xl:hidden mt-4 border-t border-border-secondary/20 pt-4">
                  <div className="flex items-center mb-3">
                    <Eye className="w-4 h-4 mr-2 text-blue-400" />
                    <h3 className="text-sm font-medium text-text-secondary">{t('workspace.previewResults')}</h3>
                  </div>

                  <div>
                    {/* 简化的统计信息 */}
                    <div className="bg-bg-tertiary rounded-lg p-3 mb-3">
                      <div className="grid grid-cols-2 gap-3 text-xs">
                        <div>
                          <span className="text-text-tertiary">{t('workspace.totalFilesShort')}</span>
                          <span className="text-text-secondary ml-1">{workflowResult.totalFiles}</span>
                        </div>
                        <div>
                          <span className="text-text-tertiary">{t('workspace.processedShort')}</span>
                          <span className="text-green-400 ml-1">{workflowResult.processedFiles}</span>
                        </div>
                        <div>
                          <span className="text-text-tertiary">{t('workspace.errorsShort')}</span>
                          <span className="text-red-400 ml-1">{workflowResult.errors.length}</span>
                        </div>
                        <div>
                          <span className="text-text-tertiary">{t('workspace.stepsShort')}</span>
                          <span className="text-blue-400 ml-1">{workflowResult.stepResults.length}</span>
                        </div>
                      </div>
                    </div>

                    {/* 主要变化预览 */}
                    <div className="space-y-2">
                      {(() => {
                        // 获取所有步骤中有变化的文件，显示前3个
                        const allChangedFiles = new Map();

                        workflowResult.stepResults.forEach(stepResult => {
                          stepResult.outputFiles.forEach(file => {
                            if (file.newPath && file.newPath !== file.path) {
                              allChangedFiles.set(file.id, file);
                            }
                          });
                        });

                        const changedFiles = Array.from(allChangedFiles.values()).slice(0, 3);

                        // 如果没有变化的文件，显示友好提示
                        if (changedFiles.length === 0) {
                          return (
                            <div key="no-changes" className="flex items-center justify-center py-6">
                              <div className="text-center">
                                <div className="w-12 h-12 bg-yellow-400/10 rounded-full flex items-center justify-center mx-auto mb-3">
                                  <AlertCircle className="w-6 h-6 text-yellow-400" />
                                </div>
                                <p className="text-text-secondary font-medium text-sm mb-2">{t('workspace.noMatchingFiles')}</p>
                                <p className="text-text-tertiary text-xs">
                                  {t('workspace.workflowStepMismatch')}
                                </p>
                              </div>
                            </div>
                          );
                        }

                        return changedFiles.map((outputFile) => {
                          // 查找该文件最初的输入状态
                          let originalFile = outputFile;
                          for (const stepResult of workflowResult.stepResults) {
                            const inputFile = stepResult.inputFiles.find(f => f.id === outputFile.id);
                            if (inputFile) {
                              originalFile = inputFile;
                              break;
                            }
                          }

                          // 获取处理该文件的步骤信息
                          const processingStep = workflowResult.stepResults.find(sr =>
                            sr.outputFiles.some(f => f.id === outputFile.id && f.newPath && f.newPath !== f.path)
                          );
                          const actionType = processingStep?.stepId ?
                            workflows.find(w => w.id === selectedWorkflowId)?.steps
                              .find(s => s.id === processingStep.stepId)?.actions[0]?.type : null;

                          return (
                            <div key={outputFile.id} className="bg-bg-tertiary rounded-lg p-2">
                              <div className="text-xs">
                                <div className="flex items-center space-x-2 mb-1">
                                  {originalFile.isDirectory ? (
                                    <Folder className="w-3 h-3 text-blue-500" />
                                  ) : (
                                    <FileText className="w-3 h-3 text-text-tertiary" />
                                  )}
                                  <p className="text-text-secondary font-medium truncate">
                                    {originalFile.name}
                                  </p>
                                </div>
                                <p className="text-green-400 truncate">
                                  → {actionType === 'rename' ?
                                      outputFile.newPath?.split(/[/\\]/).pop() :
                                      outputFile.newPath}
                                </p>
                              </div>
                            </div>
                          );
                        });
                      })()}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>

          </Card>
        </div>

        {/* 右侧：预览区域 */}
        <div className="xl:col-span-1 lg:hidden xl:block flex flex-col">
          <Card className="h-full bg-bg-secondary border-border-primary flex flex-col shadow-lg backdrop-blur-sm">
            <CardHeader className="flex-shrink-0">
              <CardTitle className="text-text-secondary flex items-center">
                <Eye className="w-5 h-5 mr-2" />
                {t('workspace.previewResults')}
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col overflow-hidden px-6 pt-2 pb-6">
              {!workflowResult ? (
                <div className="flex items-center justify-center flex-1 min-h-0">
                  <div className="text-center">
                    <Eye className="w-16 h-16 text-text-tertiary mx-auto mb-4" />
                    <p className="text-text-tertiary">{t('workspace.noPreview')}</p>
                    <p className="text-sm text-text-tertiary mt-2">{t('workspace.clickPreview')}</p>
                  </div>
                </div>
              ) : (
                <div className="flex-1 min-h-0 flex flex-col space-y-3">
                  {/* 预览统计 */}
                  <div className="bg-bg-tertiary rounded-lg p-4 flex-shrink-0">
                    <h4 className="text-sm font-medium text-text-secondary mb-2">{t('workspace.executionStats')}</h4>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <span className="text-text-tertiary">{t('workspace.totalFilesLabel')}</span>
                        <span className="text-text-secondary ml-1">{workflowResult.totalFiles}</span>
                      </div>
                      <div>
                        <span className="text-text-tertiary">{t('workspace.processedFilesLabel')}</span>
                        <span className="text-green-400 ml-1">{workflowResult.processedFiles}</span>
                      </div>
                      <div>
                        <span className="text-text-tertiary">{t('workspace.errorCountLabel')}</span>
                        <span className="text-red-400 ml-1">{workflowResult.errors.length}</span>
                      </div>
                      <div>
                        <span className="text-text-tertiary">{t('workspace.stepCountLabel')}</span>
                        <span className="text-blue-400 ml-1">{workflowResult.stepResults.length}</span>
                      </div>
                    </div>
                  </div>

                  {/* 文件变化预览 */}
                  <div className="flex-shrink-0" style={{ height: '49vh' }}>
                    <h4 className="text-sm font-medium text-text-secondary mb-1">
                      {isPreviewMode ? t('workspace.previewChanges') : t('workspace.executionResults')}
                    </h4>
                    <div className="space-y-2 overflow-y-auto scrollbar-hide" style={{ height: 'calc(49vh - 2rem)' }}>
                    {isPreviewMode ? (
                      // 预览模式：根据动作类型显示不同的变化信息
                      (() => {
                        // 使用优化的预览文件计算
                        const changedFiles = previewChangedFiles;

                        // 如果没有变化的文件，显示友好提示
                        if (changedFiles.length === 0) {
                          return (
                            <div className="flex items-center justify-center py-8">
                              <div className="text-center">
                                <div className="w-16 h-16 bg-yellow-400/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                  <AlertCircle className="w-8 h-8 text-yellow-400" />
                                </div>
                                <p className="text-text-secondary font-medium mb-2">{t('workspace.noMatchingFiles')}</p>
                                <p className="text-text-tertiary text-sm">
                                  {t('workspace.workflowStepMismatch')}
                                </p>
                              </div>
                            </div>
                          );
                        }

                        return changedFiles.map((outputFile) => {
                          // 查找该文件最初的输入状态
                          let originalFile = outputFile;
                          for (const stepResult of workflowResult.stepResults) {
                            const inputFile = stepResult.inputFiles.find(f => f.id === outputFile.id);
                            if (inputFile) {
                              originalFile = inputFile;
                              break;
                            }
                          }

                          // 获取处理该文件的步骤信息
                          const processingStep = workflowResult.stepResults.find(sr =>
                            sr.outputFiles.some(f => f.id === outputFile.id && f.newPath && f.newPath !== f.path)
                          );
                          const actionType = processingStep?.stepId ?
                            workflows.find(w => w.id === selectedWorkflowId)?.steps
                              .find(s => s.id === processingStep.stepId)?.actions[0]?.type : null;

                        return (
                          <div key={outputFile.id} className="bg-bg-tertiary rounded-lg p-3">
                            <div className="text-xs">
                              <div className="flex items-center space-x-2 mb-1">
                                {originalFile.isDirectory ? (
                                  <Folder className="w-3 h-3 text-blue-500" />
                                ) : (
                                  <FileText className="w-3 h-3 text-text-tertiary" />
                                )}
                                <p className="text-text-secondary font-medium truncate">
                                  {originalFile.name}
                                </p>
                              </div>

                              {/* 根据动作类型显示不同的预览信息 */}
                              {actionType === 'rename' ? (
                                // 重命名操作：显示文件名变化
                                <>
                                  <p className="text-text-tertiary truncate mb-1">
                                    {t('workspace.originalName')} {originalFile.name}
                                  </p>
                                  <p className="text-green-400 truncate">
                                    → {outputFile.newPath?.split(/[/\\]/).pop() || outputFile.newPath}
                                  </p>
                                </>
                              ) : (
                                // 其他操作：显示路径变化
                                <>
                                  <p className="text-text-tertiary truncate mb-1">
                                    {t('workspace.originalPath')} {originalFile.path}
                                  </p>
                                  <p className="text-green-400 truncate">
                                    → {outputFile.newPath}
                                  </p>
                                </>
                              )}

                              {outputFile?.error && (
                                <p className="text-red-400 mt-1">
                                  {t('workspace.errorPrefix')}{translateErrorMessage(outputFile.error, language)}
                                </p>
                              )}
                            </div>
                          </div>
                        )
                      })
                      })()
                    ) : (
                      // 执行模式：显示原始位置和当前位置
                      (() => {
                        // 使用优化的执行文件计算
                        const displayFiles = executionDisplayFiles;

                        // 如果没有文件显示，显示友好提示
                        if (displayFiles.length === 0) {
                          return (
                            <div className="flex items-center justify-center py-8">
                              <div className="text-center">
                                <div className="w-16 h-16 bg-yellow-400/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                  <AlertCircle className="w-8 h-8 text-yellow-400" />
                                </div>
                                <p className="text-text-secondary font-medium mb-2">{t('workspace.noProcessingResult')}</p>
                                <p className="text-text-tertiary text-sm">
                                  {t('workspace.workflowCompleteNoFiles')}
                                </p>
                              </div>
                            </div>
                          );
                        }

                        return displayFiles.map((finalFile) => {
                          // 查找该文件最初的输入状态
                          let originalFile = finalFile;
                          for (const stepResult of workflowResult.stepResults) {
                            const inputFile = stepResult.inputFiles.find(f => f.id === finalFile.id);
                            if (inputFile) {
                              originalFile = inputFile;
                              break;
                            }
                          }

                          return (
                            <div key={finalFile.id} className="bg-bg-tertiary rounded-lg p-3">
                              <div className="text-xs">
                                <div className="flex items-center space-x-2 mb-1">
                                  {finalFile.isDirectory ? (
                                    <Folder className="w-3 h-3 text-blue-500" />
                                  ) : (
                                    <FileText className="w-3 h-3 text-text-tertiary" />
                                  )}
                                  <p className="text-text-secondary font-medium truncate">
                                    {finalFile.name}
                                  </p>
                                </div>

                                {/* 显示原始位置 */}
                                <p className="text-text-tertiary truncate mb-1">
                                  {t('workspace.originalPath')} {originalFile.path}
                                </p>

                                {/* 显示当前位置 */}
                                {finalFile.path !== originalFile.path ? (
                                  <p className="text-green-400 truncate mb-1">
                                    {t('workspace.currentLocation')}{finalFile.path}
                                  </p>
                                ) : (
                                  <p className="text-yellow-400 truncate mb-1">
                                    {t('workspace.noChange')}
                                  </p>
                                )}

                                {finalFile.error && (
                                  <p className="text-red-400 mt-1">
                                    {t('workspace.errorPrefix')}{translateErrorMessage(finalFile.error, language)}
                                  </p>
                                )}
                              </div>
                            </div>
                          );
                        });
                      })()
                    )}
                    {(() => {
                      // 获取所有步骤中有变化的文件总数
                      const allChangedFiles = new Map();

                      workflowResult.stepResults.forEach(stepResult => {
                        stepResult.outputFiles.forEach(file => {
                          if (file.newPath && file.newPath !== file.path) {
                            allChangedFiles.set(file.id, file);
                          }
                        });
                      });

                      const totalChangedFiles = allChangedFiles.size;
                      const displayedFiles = isPreviewMode ? 3 : 20; // 预览模式显示3个，详细模式显示20个
                      const remainingFiles = totalChangedFiles - displayedFiles;

                      return remainingFiles > 0 ? (
                        <div className="text-xs text-text-tertiary text-center py-2 border-t border-border-secondary/20">
                          ...{t('workspace.moreChanges', { count: remainingFiles })}
                        </div>
                      ) : null;
                    })()}
                    </div>
                  </div>

                  {/* 步骤结果 */}
                  <div className="flex-shrink-0" style={{ height: '20vh' }}>
                    <h4 className="text-sm font-medium text-text-secondary mb-2">{t('workspace.stepExecutionDetails')}</h4>
                    <div className="space-y-2 overflow-y-auto scrollbar-hide" style={{ height: 'calc(20vh - 2rem)' }}>
                        {workflowResult.stepResults.map((stepResult, index) => {
                          // 分别统计输入和输出的文件和文件夹数量
                          const inputFileCount = stepResult.inputFiles.filter(f => !f.isDirectory).length;
                          const inputFolderCount = stepResult.inputFiles.filter(f => f.isDirectory).length;
                          const outputFileCount = stepResult.outputFiles.filter(f => !f.isDirectory).length;
                          const outputFolderCount = stepResult.outputFiles.filter(f => f.isDirectory).length;

                          // 只统计在当前步骤中实际被处理的文件（有newPath且与原路径不同）
                          const processedFileCount = stepResult.outputFiles.filter(f =>
                            !f.isDirectory &&
                            f.newPath &&
                            f.newPath !== f.path &&
                            // 确保这个文件在当前步骤中被处理，而不是从前面步骤继承的
                            stepResult.inputFiles.some(inputFile => inputFile.id === f.id && inputFile.path !== f.newPath)
                          ).length;

                          const processedFolderCount = stepResult.outputFiles.filter(f =>
                            f.isDirectory &&
                            f.newPath &&
                            f.newPath !== f.path &&
                            // 确保这个文件夹在当前步骤中被处理，而不是从前面步骤继承的
                            stepResult.inputFiles.some(inputFile => inputFile.id === f.id && inputFile.path !== f.newPath)
                          ).length;

                          return (
                            <div key={index} className="bg-bg-tertiary rounded-lg p-3">
                              <h5 className="text-xs font-medium text-text-secondary mb-2">
                                {t('workspace.stepNumber', { number: index + 1, name: stepResult.stepName })}
                              </h5>
                              <div className="text-xs text-text-tertiary space-y-1">
                                {/* 输入统计 */}
                                <div>
                                  <span>{t('workspace.input')}: </span>
                                  {inputFileCount === 0 && inputFolderCount === 0 ? (
                                    <span className="text-yellow-400">{t('workspace.none')}</span>
                                  ) : (
                                    <>
                                      {inputFileCount > 0 && <span>{inputFileCount} {t('workspace.files')}</span>}
                                      {inputFileCount > 0 && inputFolderCount > 0 && <span>, </span>}
                                      {inputFolderCount > 0 && <span>{inputFolderCount} {t('workspace.folders')}</span>}
                                    </>
                                  )}
                                </div>

                                {/* 输出统计 */}
                                <div>
                                  <span>{t('workspace.output')}: </span>
                                  {outputFileCount === 0 && outputFolderCount === 0 ? (
                                    <span className="text-yellow-400">{t('workspace.none')}</span>
                                  ) : (
                                    <>
                                      {outputFileCount > 0 && <span>{outputFileCount} {t('workspace.files')}</span>}
                                      {outputFileCount > 0 && outputFolderCount > 0 && <span>, </span>}
                                      {outputFolderCount > 0 && <span>{outputFolderCount} {t('workspace.folders')}</span>}
                                    </>
                                  )}
                                </div>

                                {/* 处理统计 */}
                                <div>
                                  <span>{t('workspace.processed')}: </span>
                                  {processedFileCount === 0 && processedFolderCount === 0 ? (
                                    <span className="text-yellow-400">{t('workspace.none')}</span>
                                  ) : (
                                    <>
                                      {processedFileCount > 0 && <span>{processedFileCount} {t('workspace.files')}</span>}
                                      {processedFileCount > 0 && processedFolderCount > 0 && <span>, </span>}
                                      {processedFolderCount > 0 && <span>{processedFolderCount} {t('workspace.folders')}</span>}
                                    </>
                                  )}
                                </div>

                                {stepResult.errors.length > 0 && (
                                  <p className="text-red-400">{t('workspace.errorsShort')} {stepResult.errors.length}</p>
                                )}
                              </div>
                            </div>
                          );
                        })}
                    </div>
                  </div>

                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
      <ConfirmDialog />
    </div>
  )
}
